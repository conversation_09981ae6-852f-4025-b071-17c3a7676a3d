<!DOCTYPE html>

<?php $page = 'bildiri'; ?>

<html lang="tr">
<head>
    <?php include("head.php"); ?>
    <meta title="Bildiri Gönderimi">
    <title>Bildiri Gönderimi | 57. Türk Pediatri Kongresi</title>
</head>

<body>
    <div class='preloder'>
        <div class='loader'>
            <div class='loader--dot'></div>
            <div class='loader--dot'></div>
            <div class='loader--dot'></div>
            <div class='loader--dot'></div>
            <div class='loader--dot'></div>
            <div class='loader--dot'></div>
            <div class='loader--text'></div>
        </div>
    </div>

    <?php include("header.php"); ?>

    <section class="breadcrumb_part parallax_bg">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <h5 class="wow fadeInRight" data-wow-delay=".3s" style="text-align: center; font-size:25px; color: #fcb651; font-weight: 700;">57. Türk Pediatri Kongresi</h5>
                        <h2 class="kid_title wow fadeInLeft text-center" data-wow-delay=".4s"><span class="">Bildiri Gönderimi</span></h2>
                </div>
            </div>
            <!--
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="breadcrumb_iner">
                        <h2>Bildiri </h2>
                    </div>
                </div>
            </div>
        -->
        </div>
        <div class="breadcrumb_animation_4">
            <div data-parallax='{"x": 30, "y": 250, "rotateZ":0}'><img alt="#" src="img/icon/banner_icon/animated_banner_4.png"></div>
        </div>
        <div class="breadcrumb_animation_5">
            <div data-parallax='{"x": 20, "y": 150, "rotateZ": 180}'><img alt="#" src="img/icon/banner_icon/animated_banner_5.png"></div>
        </div>
        <div class="breadcrumb_animation_7">
            <div data-parallax='{"x": 100, "y": 250, "rotateZ":0}'><img alt="#" src="img/icon/banner_icon/animated_banner_15.png"></div>
        </div>
        <div class="breadcrumb_animation_10">
            <div data-parallax='{"x": 15, "y": 150, "rotateZ":0}'><img alt="#" src="img/icon/banner_icon/animated_banner_10.png"></div>
        </div>
        <div class="breadcrumb_animation_13">
            <div data-parallax='{"x": 10, "y": 250, "rotateZ": 180}'><img alt="#" src="img/icon/banner_icon/animated_banner_21.png"></div>
        </div>
    </section>
    <section class="about_section section_padding s4_about_section">
        <div class="container custom_container">
            <div class="row justify-content-between align-items-center">
                <div class="col-lg-12 col-xl-12 order-2 order-lg-1">
                    <div class="about_section_content mt-5 mt-lg-0">
                        <!--
                        <h5 class="wow fadeInRight" data-wow-delay=".3s" style="text-align: center; font-size:25px;">57. Türk Pediatri Kongresi</h5>
                    
                        <h2 class="kid_title wow fadeInLeft text-center" data-wow-delay=".4s"><span class="title_overlay_effect">Bildiri</span></h2>
                        -->
                        <div class="text-center">
                            <a class="btn btn-primary bildiri-button" target="_blank" href="https://solo.digiabstract.com/turkpediatri2022/" style="font-size: 24px;">Bildiri Göndermek İçin Tıklayınız</a>
                        </div>
                        <h5 class="mt-5">Bildiri Teslim Bilgileri</h5>
                        <ul>
                            <li>Bildiri son gönderim tarihi: 8 Nisan 2022, saat 23:59</li>
                            <li> Sunumunuz özgün olmalıdır.</li>
                            <li>Sunumunuz 300 kelimeyi geçmemelidir.</li>
                            <li>Sunumunuzun gönderimi son bildiri gönderim tarihini geçmemelidir. Son bildiri tarihinden sonra gönderilen bildiriler kabul edilmeyecektir</li>
                            <li>Bilimsel Komite bildirileri Sözlü ve/veya Poster olarak kabul edebileceklerdir, bildiri sahipleri bu konuda bilgilendirilecektir</li>
                            <li>Sunum gönderilmeden önce bildiri sahibi ile bildiride yer alan yazarlar bildirinin son hali için emin olmalıdır, ardından bildiri sistemine yüklenmelidir.</li>
                            <li>Sunum yapacak kişinin katılımcı olarak kayıt yaptırması gerekmektedir</li>
                        </ul>
                        <h5>Sunum Hazırlama</h5>
                        <p>Başlamadan önce, lütfen aşağıdaki adımları izleyiniz:</p>
                        <ul>

<!--                            <li>Poster boyutu 70 cm (yatay) * 100 cm (dikey) olmalıdır.</li>-->
                            <li>Sunumu gerçekleştirecek kişinin iletişim bilgilerini eksiksiz yazmanız gerekmektedir</li>
                            <li>E-posta adresi</li>
                            <li>Adres bilgileri</li>
                            <li>Gün içerisinde ve akşam ulaşılabilecek telefon numaraları</li>
                            <li>Yazar ve diğer yazarların bilgileri detaylı olarak yer almalıdır</li>
                            <li>Tam olarak isim ve soyadı bilgileri</li>
                            <li>Yazar ismi büyük harf ve küçük harf içermelidir. (J. Kaya)</li>
                            <li>Kurum bilginiz; bölüm, enstitü / hastane, şehir, ülke şeklinde olmalıdır.</li>
                            <li>Sunum başlığınız 30 kelimeyle sınırlı olmalı ve BÜYÜK HARFLER kullanılmalıdır.</li>
                            <li>Sunumunuz “Teşekkür” dahil olmak üzere 300 kelimeden oluşmalıdır</li>
                        </ul>
                        <p><b>NOT:</b> Yazınızın düzenlenmesinde, edit edilmesinde ve kelime hesaplamasında Office programları kullanmanızı öneririz. (Örn. Word kelime işlemcisi vb.)</p>
                        <ul>
                            <li>Bildirilerde şu esaslar açıkça belirtilmelidir:
                                <ul>
                                    <li>Deneyim ve hedefler</li>
                                    <li>Metotlar</li>
                                    <li>Sonuçlar</li>
                                    <li>Kararlar</li>
                                </ul>
                            </li>
                            <li>Yalnızca standart kısaltmalar kullanın. Özel bir yer ya da beklenmedik kısaltmalar parantez içerisinde açıklamalarıyla belirtilmelidir</li>
                            <li>İlaç isimleri markalarıyla belirtilmelidir. Sayılarını adet olarak açıklayınız.</li>
                            <li>Bildiri özetlerinde kesinlikle tablo ve resim bulunmayacaktır.</li>
                            <li>Açıklama</li>
                            <li>Yazarın finansal bağlantısı var ise belirtmesi gerekir. Herhangi bir reklam ürününün üreticisi/tedarikçi ya da işiyle ilişkili bir hizmet sunu formunda uygun bir kutu içerisinde belirtilmelidir</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <?php include("footer.php"); ?>

    <?php include("script-2.php"); ?>

</body>
</html>