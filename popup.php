<div class="modal micromodal-slide" id="modal-1" aria-hidden="false">
    <div class="modal__overlay" tabindex="-1" data-micromodal-close>
        <div class="modal__container" role="dialog" aria-modal="true" aria-labelledby="modal-1-title" style="">
            <header class="modal__header" style="height: 20px;">
                <h2 class="modal__title" id="modal-1-title">
                </h2>
                <button class="modal__close" aria-label="Close modal" data-micromodal-close></button>
            </header>
            <main class="modal__content" id="modal-1-content">

                <style> iframe{width: 625px;}</style>
				<div class="d-flex flex-column">
					<a target="_blank" href="doc/57tpkkitap.pdf?v=1378"><img alt="" src="doc/57tpk-slogan.png" style="" class="modal-image"></a>
                	<a class="btn btn-primary text-white" target="_blank" href="doc/57tpkkitap.pdf?v=1378">Bildiri Özetleri Kitabını İndirmek İçin Tıklayınız</a>
				</div>
            </main>
            <footer class="modal__footer text-center">
            </footer>
        </div>
    </div>
</div>
<script src="https://unpkg.com/micromodal/dist/micromodal.min.js"></script>
<script>
    MicroModal.show('modal-1',
        {
            onShow: modal => console.info(`${modal.id} is shown`), // [1]
            onClose: modal => $('#modal-1-content').html(''), // [2]
            openClass: 'is-open', // [5]
            disableScroll: true, // [6]
            disableFocus: false, // [7]
            awaitOpenAnimation: false, // [8]
            awaitCloseAnimation: false, // [9]
            debugMode: true // [10]
        }
    );
</script>
<link rel="stylesheet" href="css/modal.css?v=2">